import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

// Google Calendar configuration
const CALENDAR_ID = '<EMAIL>';
const TIMEZONE = 'Europe/London';

// Service Account credentials
const SERVICE_ACCOUNT_CREDENTIALS = {
  type: "service_account",
  project_id: "raising-my-rescue-session",
  private_key_id: "a76e798907546e4569e14c0c32c966b5816aaa67",
  private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n') || "",
  client_email: "<EMAIL>",
  client_id: "110484167377984707130",
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: "https://www.googleapis.com/robot/v1/metadata/x509/raising-my-rescue%40raising-my-rescue-session.iam.gserviceaccount.com",
  universe_domain: "googleapis.com"
};

// Initialize Google Calendar API
const getCalendarClient = () => {
  const auth = new google.auth.GoogleAuth({
    credentials: SERVICE_ACCOUNT_CREDENTIALS,
    scopes: ['https://www.googleapis.com/auth/calendar']
  });

  return google.calendar({ version: 'v3', auth });
};

// Helper function to calculate session end time (1.5 hour sessions)
const calculateEndTime = (date: string, time: string): string => {
  const startDateTime = new Date(`${date}T${time}:00`);
  const endDateTime = new Date(startDateTime.getTime() + 90 * 60 * 1000); // Add 1.5 hours (90 minutes)
  return endDateTime.toISOString().slice(0, 19); // Remove milliseconds and Z
};

// Helper function to format session title
const formatSessionTitle = (clientName: string, dogName: string): string => {
  return `${clientName}${dogName ? ` w/ ${dogName}` : ''}`;
};

// Helper function to format session description
const formatSessionDescription = (sessionType: string): string => {
  return sessionType;
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Calendar create API called with:', JSON.stringify(body, null, 2));

    const {
      clientName,
      clientEmail,
      clientAddress,
      dogName,
      sessionType,
      bookingDate,
      bookingTime,
      notes,
      quote
    } = body;

    // Validate required fields
    if (!clientName || !sessionType || !bookingDate || !bookingTime) {
      console.error('Missing required fields:', {
        clientName: !!clientName,
        sessionType: !!sessionType,
        bookingDate: !!bookingDate,
        bookingTime: !!bookingTime
      });
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    console.log('Validation passed, initializing calendar client...');

    const calendar = getCalendarClient();
    console.log('Calendar client initialized successfully');

    const startDateTime = `${bookingDate}T${bookingTime}:00`;
    const endDateTime = calculateEndTime(bookingDate, bookingTime);
    console.log('Date/time calculated:', { startDateTime, endDateTime });

    const event = {
      summary: formatSessionTitle(clientName, dogName),
      description: formatSessionDescription(sessionType),
      location: clientAddress || '',
      start: {
        dateTime: startDateTime,
        timeZone: TIMEZONE,
      },
      end: {
        dateTime: endDateTime,
        timeZone: TIMEZONE,
      },
      // Removed attendees field - service accounts can't invite attendees without Domain-Wide Delegation
    };

    console.log('Event object created:', JSON.stringify(event, null, 2));
    console.log('Attempting to create calendar event...');

    const response = await calendar.events.insert({
      calendarId: CALENDAR_ID,
      requestBody: event,
    });

    const eventId = response.data.id;
    console.log('Successfully created calendar event:', eventId);
    
    return NextResponse.json({
      success: true,
      message: 'Calendar event created successfully',
      eventId: eventId
    });

  } catch (error) {
    console.error('Error creating calendar event:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create calendar event',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
