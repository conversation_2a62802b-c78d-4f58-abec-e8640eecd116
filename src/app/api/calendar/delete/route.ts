import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

// Google Calendar configuration
const CALENDAR_ID = '<EMAIL>';

// Service Account credentials
const SERVICE_ACCOUNT_CREDENTIALS = {
  type: "service_account",
  project_id: "raising-my-rescue-session",
  private_key_id: "a76e798907546e4569e14c0c32c966b5816aaa67",
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  client_email: "<EMAIL>",
  client_id: "110484167377984707130",
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: "https://www.googleapis.com/robot/v1/metadata/x509/raising-my-rescue%40raising-my-rescue-session.iam.gserviceaccount.com",
  universe_domain: "googleapis.com"
};

// Initialize Google Calendar API
const getCalendarClient = () => {
  const auth = new google.auth.GoogleAuth({
    credentials: SERVICE_ACCOUNT_CREDENTIALS,
    scopes: ['https://www.googleapis.com/auth/calendar']
  });

  return google.calendar({ version: 'v3', auth });
};

export async function POST(request: NextRequest) {
  let eventId: string | undefined;

  try {
    const body = await request.json();
    console.log('Deleting Google Calendar event:', body);

    eventId = body.eventId;

    // Validate required fields
    if (!eventId) {
      return NextResponse.json(
        { error: 'Missing eventId' },
        { status: 400 }
      );
    }

    const calendar = getCalendarClient();

    await calendar.events.delete({
      calendarId: CALENDAR_ID,
      eventId: eventId,
    });

    console.log('Successfully deleted calendar event:', eventId);

    return NextResponse.json({
      success: true,
      message: 'Calendar event deleted successfully'
    });

  } catch (error) {
    // If event doesn't exist, that's okay - it might have been deleted already
    if (error instanceof Error && error.message.includes('404')) {
      console.log('Calendar event not found (already deleted):', eventId || 'unknown');
      return NextResponse.json({
        success: true,
        message: 'Calendar event not found (already deleted)'
      });
    }

    console.error('Failed to delete calendar event:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete calendar event',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
